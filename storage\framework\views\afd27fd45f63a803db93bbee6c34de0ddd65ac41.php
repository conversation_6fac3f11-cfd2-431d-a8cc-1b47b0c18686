<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <title><?php echo $__env->yieldContent('title', 'Dashboard'); ?> - RSPPU PPDS RSK Dharmais</title>
    <meta name="description" content="">
    <meta name="keywords" content="">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <!-- Favicons -->
    <link href="<?php echo e(asset('assets/img/dharmais_icon.png')); ?>" rel="icon">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com" rel="preconnect">
    <link href="https://fonts.gstatic.com" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Raleway:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">

    <!-- Vendor CSS Files -->
    <link href="<?php echo e(asset('assets/vendor/bootstrap/css/bootstrap.min.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(asset('assets/vendor/bootstrap-icons/bootstrap-icons.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(asset('assets/vendor/aos/aos.css')); ?>" rel="stylesheet">

    <!-- Main CSS File -->
    <link href="<?php echo e(asset('assets/css/main.css')); ?>" rel="stylesheet">

    <style>
        /* Medical/Hospital Theme Color Variables */
        :root {
            --medical-primary: #1e3a8a;
            --medical-secondary: #3b82f6;
            --medical-light: #60a5fa;
            --medical-accent: #0ea5e9;
            --medical-bg: #f8fafc;
            --medical-surface: #ffffff;
            --medical-border: #e2e8f0;
            --medical-text: #1e293b;
            --medical-text-light: #64748b;
        }

        body {
            background-color: var(--medical-bg);
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }

        .navbar-brand img {
            max-height: 40px;
        }

        .bg-gradient-primary {
            background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-secondary) 100%);
        }

        .card {
            transition: transform 0.2s ease-in-out;
            border: 1px solid var(--medical-border);
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        /* Sidebar Styling with Medical Theme - Fixed Position */
        .sidebar-container {
            position: fixed;
            top: 60px;
            left: 0;
            width: 280px;
            height: calc(100vh - 60px);
            z-index: 1000;
            transition: transform 0.3s ease;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar {
            width: 100%;
            height: 100%;
            background: linear-gradient(180deg, var(--medical-primary) 0%, var(--medical-secondary) 100%);
            padding: 1.5rem;
            overflow-y: auto;
            margin: 0;
            border: none;
            display: flex;
            flex-direction: column;
        }

        /* Sidebar Profile Section */
        .sidebar-profile {
            flex-shrink: 0;
        }

        .profile-avatar {
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid rgba(255,255,255,0.2);
        }

        .profile-avatar i {
            font-size: 3rem;
        }

        .profile-name {
            font-weight: 600;
            font-size: 1rem;
        }

        .profile-role {
            font-size: 0.875rem;
        }

        /* Sidebar Navigation */
        .sidebar-nav {
            flex: 1;
            overflow-y: auto;
        }

        .sidebar-nav .nav {
            padding: 0;
            margin: 0;
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.9);
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            margin: 0.25rem 0;
            transition: all 0.3s ease;
            border: 1px solid transparent;
            display: flex;
            align-items: center;
            text-decoration: none;
            font-weight: 500;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.15);
            transform: translateX(5px);
            border-color: rgba(255, 255, 255, 0.2);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .sidebar .nav-link i {
            width: 20px;
            text-align: center;
            margin-right: 0.75rem;
            font-size: 1.1rem;
        }

        .sidebar .nav-link span {
            font-size: 0.95rem;
        }

        /* Sidebar Footer */
        .sidebar-footer {
            flex-shrink: 0;
            border-top: 1px solid rgba(255,255,255,0.1);
            padding-top: 1rem;
        }

        /* Content area adjustment */
        .main-content {
            margin-left: 280px;
            transition: margin-left 0.3s ease;
            min-height: calc(100vh - 60px);
        }

        /* Hamburger Menu Button */
        .sidebar-toggle {
            background: none;
            border: none;
            color: var(--medical-primary);
            font-size: 1.5rem;
            padding: 0.5rem;
            border-radius: 0.375rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .sidebar-toggle:hover {
            background-color: var(--medical-border);
            color: var(--medical-secondary);
        }

        /* Sidebar States */
        .sidebar-hidden .sidebar-container {
            transform: translateX(-100%);
        }

        .sidebar-hidden .main-content {
            margin-left: 0;
        }

        /* Sidebar Overlay */
        .sidebar-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            width: 100vw;
            height: calc(100vh - 60px);
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999;
            display: none;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .sidebar-overlay.show {
            display: block;
            opacity: 1;
        }

        /* Responsive Behavior */
        @media (max-width: 768px) {
            .sidebar-container {
                transform: translateX(-100%);
            }

            .sidebar-container.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0 !important;
            }
        }

        @media (min-width: 769px) {
            .sidebar-overlay {
                display: none !important;
            }

            /* Ensure sidebar is always visible on desktop */
            .sidebar-container {
                transform: translateX(0);
            }
        }

        /* Scrollbar styling for sidebar */
        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.1);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255,255,255,0.5);
        }

        #video {
            border: 3px solid var(--medical-border);
            border-radius: 10px;
        }

        .btn-lg {
            padding: 0.75rem 1.5rem;
            font-size: 1.1rem;
            background-color: var(--medical-secondary);
            border-color: var(--medical-secondary);
        }

        .btn-lg:hover {
            background-color: var(--medical-primary);
            border-color: var(--medical-primary);
        }

        .table th {
            border-top: none;
            font-weight: 600;
            color: var(--medical-text);
            background-color: var(--medical-bg);
        }

        .badge {
            font-size: 0.75rem;
        }

        .alert {
            border: none;
            border-radius: 10px;
        }

        .rounded-circle {
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Footer Styling */
        .footer {
            background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-secondary) 100%);
            color: white;
            padding: 2rem 0 1rem 0;
            margin-top: auto;
        }

        .footer a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer a:hover {
            color: white;
        }

        /* Layout Structure */
        .wrapper {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .main-container {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        /* Remove default Bootstrap margins/padding that might cause gaps */
        .container-fluid {
            padding-left: 0;
            padding-right: 0;
            margin: 0;
        }

        /* Remove any default margins/padding */
        * {
            box-sizing: border-box;
        }

        html, body {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
        }

        /* Navbar styling */
        .navbar {
            margin: 0;
            padding: 0 1rem;
            border: none;
        }
    </style>
</head>

<body class="wrapper">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-white shadow-sm" style="height: 60px;">
        <div class="container-fluid">
            <!-- Hamburger Menu Button -->
            <button class="sidebar-toggle me-3" type="button" id="sidebarToggle">
                <i class="bi bi-list"></i>
            </button>

            <a class="navbar-brand d-flex align-items-center" href="#">
                <img src="<?php echo e(asset('assets/img/dharmais_logo.png')); ?>" alt="Logo" class="me-2">
                <span class="text-dark fw-bold">PPDS Dashboard</span>
            </a>

            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-dark d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle me-2 fs-5" style="color: var(--medical-secondary);"></i>
                        <span><?php echo e(session('nama_lengkap', session('username'))); ?></span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#"><i class="bi bi-person me-2"></i>Profile</a></li>
                        <li><a class="dropdown-item" href="#"><i class="bi bi-gear me-2"></i>Settings</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <form action="<?php echo e(route('logout')); ?>" method="POST" class="d-inline">
                                <?php echo csrf_field(); ?>
                                <button type="submit" class="dropdown-item text-danger">
                                    <i class="bi bi-box-arrow-right me-2"></i>Logout
                                </button>
                            </form>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Include Sidebar Partial -->
    <?php echo $__env->make('layouts.partials.sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <!-- Main Content -->
    <div class="main-content">
        <div class="container-fluid">
            <div class="p-4">
                <?php echo $__env->yieldContent('content'); ?>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <?php echo $__env->make('layouts.partials.footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <!-- Loading Modal -->
    <div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center py-4">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mb-0">Sedang memproses presensi...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1100;">
        <div id="successToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header bg-success text-white">
                <i class="bi bi-check-circle me-2"></i>
                <strong class="me-auto">Berhasil</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body"></div>
        </div>

        <div id="errorToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header bg-danger text-white">
                <i class="bi bi-exclamation-triangle me-2"></i>
                <strong class="me-auto">Error</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body"></div>
        </div>
    </div>

    <!-- Vendor JS Files -->
    <script src="<?php echo e(asset('assets/vendor/bootstrap/js/bootstrap.bundle.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/aos/aos.js')); ?>"></script>

    <!-- Main JS File -->
    <script src="<?php echo e(asset('assets/js/main.js')); ?>"></script>

    <script>
        // Sidebar Toggle Functionality
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebarContainer = document.getElementById('sidebarContainer');
            const sidebarOverlay = document.getElementById('sidebarOverlay');
            const body = document.body;

            let sidebarVisible = window.innerWidth > 768;

            // Initialize sidebar state
            function initSidebar() {
                if (window.innerWidth <= 768) {
                    body.classList.add('sidebar-hidden');
                    sidebarVisible = false;
                } else {
                    body.classList.remove('sidebar-hidden');
                    sidebarVisible = true;
                }
            }

            // Toggle sidebar
            function toggleSidebar() {
                if (window.innerWidth <= 768) {
                    // Mobile behavior
                    sidebarContainer.classList.toggle('show');
                    sidebarOverlay.classList.toggle('show');
                } else {
                    // Desktop behavior
                    body.classList.toggle('sidebar-hidden');
                    sidebarVisible = !sidebarVisible;
                }
            }

            // Event listeners
            sidebarToggle.addEventListener('click', toggleSidebar);

            // Close sidebar when clicking overlay (mobile)
            sidebarOverlay.addEventListener('click', function() {
                sidebarContainer.classList.remove('show');
                sidebarOverlay.classList.remove('show');
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth > 768) {
                    // Desktop mode
                    sidebarContainer.classList.remove('show');
                    sidebarOverlay.classList.remove('show');
                    if (!sidebarVisible) {
                        body.classList.add('sidebar-hidden');
                    } else {
                        body.classList.remove('sidebar-hidden');
                    }
                } else {
                    // Mobile mode
                    body.classList.add('sidebar-hidden');
                }
            });

            // Close sidebar when clicking nav links on mobile
            const navLinks = sidebarContainer.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', function() {
                    if (window.innerWidth <= 768) {
                        sidebarContainer.classList.remove('show');
                        sidebarOverlay.classList.remove('show');
                    }
                });
            });

            // Initialize
            initSidebar();
        });

        // Toast helper functions
        function showSuccessToast(message) {
            const toast = document.getElementById('successToast');
            toast.querySelector('.toast-body').textContent = message;
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
        }

        function showErrorToast(message) {
            const toast = document.getElementById('errorToast');
            toast.querySelector('.toast-body').textContent = message;
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
        }

        // Smooth scrolling functions
        function scrollToPresensi() {
            document.querySelector('[data-section="presensi"]')?.scrollIntoView({ behavior: 'smooth' });
        }

        function scrollToHistory() {
            document.querySelector('[data-section="history"]')?.scrollIntoView({ behavior: 'smooth' });
        }

        // Initialize AOS
        AOS.init();

        // Auto-refresh time
        setInterval(function() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('id-ID');
            document.querySelectorAll('.current-time').forEach(el => {
                el.textContent = timeString;
            });
        }, 1000);
    </script>

    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\laragon\www\rsppu\resources\views/layouts/dashboard.blade.php ENDPATH**/ ?>