<?php $__env->startSection('title', 'Dashboard PPDS'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-gradient-primary text-white">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-1">Selamat Datang, <?php echo e($user->nama_lengkap); ?>!</h2>
                            <p class="mb-0">Dashboard Presensi PPDS RSPPU RS Kanker Dharmais</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="text-white-50">
                                <i class="bi bi-calendar-date fs-1"></i>
                            </div>
                            <div class="mt-2">
                                <small><?php echo e(\Carbon\Carbon::now()->format('l, d F Y')); ?></small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Presensi Hari Ini -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-gradient rounded-circle p-3">
                                <i class="bi bi-clock text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-1">Status Presensi Hari Ini</h6>
                            <?php if($todayAttendance): ?>
                                <?php if($todayAttendance->jam_masuk && !$todayAttendance->jam_keluar): ?>
                                    <span class="badge bg-warning">Sudah Masuk</span>
                                    <small class="text-muted d-block">Masuk: <?php echo e($todayAttendance->jam_masuk->format('H:i')); ?></small>
                                <?php elseif($todayAttendance->jam_masuk && $todayAttendance->jam_keluar): ?>
                                    <span class="badge bg-success">Lengkap</span>
                                    <small class="text-muted d-block">
                                        Masuk: <?php echo e($todayAttendance->jam_masuk->format('H:i')); ?> | 
                                        Keluar: <?php echo e($todayAttendance->jam_keluar->format('H:i')); ?>

                                    </small>
                                <?php endif; ?>
                            <?php else: ?>
                                <span class="badge bg-danger">Belum Presensi</span>
                                <small class="text-muted d-block">Silakan lakukan presensi masuk</small>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-info bg-gradient rounded-circle p-3">
                                <i class="bi bi-geo-alt text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-1">Lokasi Saat Ini</h6>
                            <span id="currentLocation" class="text-muted">Mendeteksi lokasi...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Presensi Actions -->
    <div class="row mb-4" data-section="presensi">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0">
                        <i class="bi bi-camera me-2"></i>Presensi dengan Kamera
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="text-center">
                                <video id="video" width="100%" height="300" autoplay style="border-radius: 10px; background: #f8f9fa;"></video>
                                <canvas id="canvas" style="display: none;"></canvas>
                                <div class="mt-3">
                                    <button id="startCamera" class="btn btn-outline-primary me-2">
                                        <i class="bi bi-camera-video me-1"></i>Aktifkan Kamera
                                    </button>
                                    <button id="capturePhoto" class="btn btn-primary me-2" disabled>
                                        <i class="bi bi-camera me-1"></i>Ambil Foto
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div id="previewSection" style="display: none;">
                                <h6>Preview Foto:</h6>
                                <img id="photoPreview" class="img-fluid rounded mb-3" style="max-height: 300px;">
                                <div class="d-grid gap-2">
                                    <?php if(!$todayAttendance): ?>
                                        <button id="checkInBtn" class="btn btn-success btn-lg">
                                            <i class="bi bi-box-arrow-in-right me-2"></i>Presensi Masuk
                                        </button>
                                    <?php elseif($todayAttendance && !$todayAttendance->jam_keluar): ?>
                                        <button id="checkOutBtn" class="btn btn-warning btn-lg">
                                            <i class="bi bi-box-arrow-left me-2"></i>Presensi Keluar
                                        </button>
                                    <?php else: ?>
                                        <div class="alert alert-success">
                                            <i class="bi bi-check-circle me-2"></i>
                                            Presensi hari ini sudah lengkap
                                        </div>
                                    <?php endif; ?>
                                    <button id="retakePhoto" class="btn btn-outline-secondary">
                                        <i class="bi bi-arrow-clockwise me-1"></i>Ambil Ulang
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Riwayat Presensi -->
    <div class="row" data-section="history">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0">
                        <i class="bi bi-clock-history me-2"></i>Riwayat Presensi
                    </h5>
                </div>
                <div class="card-body">
                    <?php if($attendanceHistory->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Tanggal</th>
                                        <th>Jam Masuk</th>
                                        <th>Jam Keluar</th>
                                        <th>Status</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $attendanceHistory; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attendance): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e($attendance->tanggal->format('d/m/Y')); ?></td>
                                        <td>
                                            <?php if($attendance->jam_masuk): ?>
                                                <span class="badge bg-success"><?php echo e($attendance->jam_masuk->format('H:i')); ?></span>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($attendance->jam_keluar): ?>
                                                <span class="badge bg-warning"><?php echo e($attendance->jam_keluar->format('H:i')); ?></span>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($attendance->jam_masuk && $attendance->jam_keluar): ?>
                                                <span class="badge bg-success">Lengkap</span>
                                            <?php elseif($attendance->jam_masuk): ?>
                                                <span class="badge bg-warning">Masuk Saja</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">Tidak Hadir</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary" onclick="viewDetail(<?php echo e($attendance->id); ?>)">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="bi bi-inbox fs-1 text-muted"></i>
                            <p class="text-muted mt-2">Belum ada riwayat presensi</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
let stream = null;
let currentLocation = null;
let capturedPhoto = null;

// Get current location
function getCurrentLocation() {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            function(position) {
                currentLocation = {
                    latitude: position.coords.latitude,
                    longitude: position.coords.longitude
                };

                // Display coordinates directly (no external API needed)
                document.getElementById('currentLocation').textContent =
                    `${currentLocation.latitude.toFixed(6)}, ${currentLocation.longitude.toFixed(6)}`;
            },
            function(error) {
                let errorMessage = 'Lokasi tidak dapat dideteksi';
                switch(error.code) {
                    case error.PERMISSION_DENIED:
                        errorMessage = 'Akses lokasi ditolak oleh user';
                        break;
                    case error.POSITION_UNAVAILABLE:
                        errorMessage = 'Informasi lokasi tidak tersedia';
                        break;
                    case error.TIMEOUT:
                        errorMessage = 'Request lokasi timeout';
                        break;
                }
                document.getElementById('currentLocation').textContent = errorMessage;
                console.error('Error getting location:', error);
            },
            {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 60000
            }
        );
    } else {
        document.getElementById('currentLocation').textContent = 'Geolocation tidak didukung browser';
    }
}

// Start camera
document.getElementById('startCamera').addEventListener('click', function() {
    const video = document.getElementById('video');

    navigator.mediaDevices.getUserMedia({ video: true })
        .then(function(mediaStream) {
            stream = mediaStream;
            video.srcObject = stream;

            document.getElementById('startCamera').disabled = true;
            document.getElementById('capturePhoto').disabled = false;
        })
        .catch(function(error) {
            console.error('Error accessing camera:', error);
            alert('Tidak dapat mengakses kamera. Pastikan Anda memberikan izin akses kamera.');
        });
});

// Capture photo
document.getElementById('capturePhoto').addEventListener('click', function() {
    const video = document.getElementById('video');
    const canvas = document.getElementById('canvas');
    const context = canvas.getContext('2d');

    // Validate video is ready
    if (video.videoWidth === 0 || video.videoHeight === 0) {
        alert('Kamera belum siap. Silakan tunggu sebentar.');
        return;
    }

    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    context.drawImage(video, 0, 0);

    // Convert to JPEG with quality compression
    capturedPhoto = canvas.toDataURL('image/jpeg', 0.8);

    // Validate photo size (max 2MB)
    const photoSize = Math.round((capturedPhoto.length * 3/4) / 1024 / 1024);
    if (photoSize > 2) {
        alert('Ukuran foto terlalu besar. Silakan coba lagi.');
        return;
    }

    // Show preview
    document.getElementById('photoPreview').src = capturedPhoto;
    document.getElementById('previewSection').style.display = 'block';

    // Stop camera
    if (stream) {
        stream.getTracks().forEach(track => track.stop());
        stream = null;
    }

    document.getElementById('startCamera').disabled = false;
    document.getElementById('capturePhoto').disabled = true;
});

// Retake photo
document.getElementById('retakePhoto').addEventListener('click', function() {
    document.getElementById('previewSection').style.display = 'none';
    capturedPhoto = null;
    document.getElementById('startCamera').click();
});

// Check in
document.getElementById('checkInBtn')?.addEventListener('click', function() {
    if (!capturedPhoto) {
        alert('Silakan ambil foto terlebih dahulu');
        return;
    }

    if (!currentLocation) {
        alert('Lokasi belum terdeteksi. Silakan tunggu sebentar.');
        return;
    }

    // Disable button to prevent double submission
    this.disabled = true;
    this.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Memproses...';

    const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
    loadingModal.show();

    const formData = new FormData();
    formData.append('foto', capturedPhoto);
    formData.append('latitude', currentLocation.latitude);
    formData.append('longitude', currentLocation.longitude);
    formData.append('alamat', document.getElementById('currentLocation').textContent);

    fetch('<?php echo e(route("presensi.masuk")); ?>', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        loadingModal.hide();
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert(data.message);
            // Re-enable button
            document.getElementById('checkInBtn').disabled = false;
            document.getElementById('checkInBtn').innerHTML = '<i class="bi bi-box-arrow-in-right me-2"></i>Presensi Masuk';
        }
    })
    .catch(error => {
        loadingModal.hide();
        console.error('Error:', error);
        alert('Terjadi kesalahan sistem');
        // Re-enable button
        document.getElementById('checkInBtn').disabled = false;
        document.getElementById('checkInBtn').innerHTML = '<i class="bi bi-box-arrow-in-right me-2"></i>Presensi Masuk';
    });
});

// Check out
document.getElementById('checkOutBtn')?.addEventListener('click', function() {
    if (!capturedPhoto) {
        alert('Silakan ambil foto terlebih dahulu');
        return;
    }

    if (!currentLocation) {
        alert('Lokasi belum terdeteksi. Silakan tunggu sebentar.');
        return;
    }

    // Disable button to prevent double submission
    this.disabled = true;
    this.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Memproses...';

    const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
    loadingModal.show();

    const formData = new FormData();
    formData.append('foto', capturedPhoto);
    formData.append('latitude', currentLocation.latitude);
    formData.append('longitude', currentLocation.longitude);
    formData.append('alamat', document.getElementById('currentLocation').textContent);

    fetch('<?php echo e(route("presensi.keluar")); ?>', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        loadingModal.hide();
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert(data.message);
            // Re-enable button
            document.getElementById('checkOutBtn').disabled = false;
            document.getElementById('checkOutBtn').innerHTML = '<i class="bi bi-box-arrow-left me-2"></i>Presensi Keluar';
        }
    })
    .catch(error => {
        loadingModal.hide();
        console.error('Error:', error);
        alert('Terjadi kesalahan sistem');
        // Re-enable button
        document.getElementById('checkOutBtn').disabled = false;
        document.getElementById('checkOutBtn').innerHTML = '<i class="bi bi-box-arrow-left me-2"></i>Presensi Keluar';
    });
});

// View detail function
function viewDetail(id) {
    // Create modal for detail view
    const modalHtml = `
        <div class="modal fade" id="detailModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Detail Presensi</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Memuat detail presensi...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('detailModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('detailModal'));
    modal.show();

    // Fetch actual detail data
    fetch(`/presensi/detail/${id}`, {
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const detail = data.data;
            document.querySelector('#detailModal .modal-body').innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>Informasi Presensi</h6>
                        <table class="table table-sm">
                            <tr><td><strong>Tanggal:</strong></td><td>${detail.tanggal}</td></tr>
                            <tr><td><strong>Jam Masuk:</strong></td><td>${detail.jam_masuk || '-'}</td></tr>
                            <tr><td><strong>Jam Keluar:</strong></td><td>${detail.jam_keluar || '-'}</td></tr>
                            <tr><td><strong>Status:</strong></td><td><span class="badge bg-${detail.status === 'keluar' ? 'success' : 'warning'}">${detail.status}</span></td></tr>
                        </table>

                        <h6 class="mt-3">Lokasi</h6>
                        <table class="table table-sm">
                            <tr><td><strong>Lokasi Masuk:</strong></td><td>${detail.lokasi_masuk || '-'}</td></tr>
                            <tr><td><strong>Alamat Masuk:</strong></td><td>${detail.alamat_masuk || '-'}</td></tr>
                            ${detail.lokasi_keluar ? `<tr><td><strong>Lokasi Keluar:</strong></td><td>${detail.lokasi_keluar}</td></tr>` : ''}
                            ${detail.alamat_keluar ? `<tr><td><strong>Alamat Keluar:</strong></td><td>${detail.alamat_keluar}</td></tr>` : ''}
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>Foto Presensi</h6>
                        ${detail.foto_masuk ? `
                            <div class="mb-3">
                                <label class="form-label"><strong>Foto Masuk:</strong></label>
                                <img src="${detail.foto_masuk}" class="img-fluid rounded" style="max-height: 200px;">
                            </div>
                        ` : ''}
                        ${detail.foto_keluar ? `
                            <div class="mb-3">
                                <label class="form-label"><strong>Foto Keluar:</strong></label>
                                <img src="${detail.foto_keluar}" class="img-fluid rounded" style="max-height: 200px;">
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
        } else {
            document.querySelector('#detailModal .modal-body').innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    ${data.message}
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        document.querySelector('#detailModal .modal-body').innerHTML = `
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle me-2"></i>
                Terjadi kesalahan saat memuat detail presensi
            </div>
        `;
    });
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    getCurrentLocation();
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.dashboard', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\rsppu\resources\views/dashboard.blade.php ENDPATH**/ ?>